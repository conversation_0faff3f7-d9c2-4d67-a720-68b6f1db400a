let currentChatId = null;
let currentChatTitle = "New Chat"; // Variable to store the current chat title

async function loadChats() {
    const res = await fetch("/chats");
    const chats = await res.json();
    const list = document.getElementById("chatList");
    list.innerHTML = "";
    chats.forEach(chat => {
        const chatItem = document.createElement("div");
        chatItem.className = "chat-item"; // Add a class for styling
        chatItem.dataset.chatId = chat.id; // Store chat ID on the element

        const chatButton = document.createElement("button");
        chatButton.textContent = chat.title;
        chatButton.onclick = () => loadChat(chat.id, chat.title); // Pass title to loadChat
        chatButton.ondblclick = (e) => { // Handle double-click for rename
            e.preventDefault(); // Prevent text selection on double click
            promptRenameChat(chat.id, chat.title);
        };
        chatButton.oncontextmenu = (e) => { // Handle right-click for delete
            e.preventDefault(); // Prevent default context menu
            showChatContextMenu(e, chat.id);
        };
        chatItem.appendChild(chatButton);

        list.appendChild(chatItem);
    });
}

// Function to show context menu for chat items
function showChatContextMenu(event, chatId) {
    // Remove any existing context menus
    const existingMenu = document.getElementById('chatContextMenu');
    if (existingMenu) {
        existingMenu.remove();
    }

    const contextMenu = document.createElement('div');
    contextMenu.id = 'chatContextMenu';
    contextMenu.className = 'context-menu';
    contextMenu.style.top = `${event.clientY}px`;
    contextMenu.style.left = `${event.clientX}px`;

    const deleteOption = document.createElement('div');
    deleteOption.className = 'context-menu-item';
    deleteOption.textContent = 'Delete Chat';
    deleteOption.onclick = () => {
        deleteChat(chatId);
        contextMenu.remove(); // Close menu after action
    };
    contextMenu.appendChild(deleteOption);

    document.body.appendChild(contextMenu);

    // Close menu when clicking outside
    document.addEventListener('click', function closeMenu(e) {
        if (!contextMenu.contains(e.target)) {
            contextMenu.remove();
            document.removeEventListener('click', closeMenu);
        }
    });
}


async function startNewChat() {
    const res = await fetch("/new_chat", { method: "POST" });
    const data = await res.json();
    currentChatId = data.chat_id;
    document.getElementById("messages").innerHTML = "";
    loadChats();
}

async function loadChat(chatId, chatTitle) {
    const res = await fetch(`/chat/${chatId}`);
    const messages = await res.json();
    currentChatId = chatId;
    currentChatTitle = chatTitle; // Set the current chat title
    document.getElementById("chatTitleArea").textContent = currentChatTitle; // Display the title

    const msgDiv = document.getElementById("messages");
    msgDiv.innerHTML = "";
    messages.forEach((msg, index) => { // Pass index to appendMessage
        appendMessage(msg.role, msg.text, index); // Use a helper function for appending and formatting
    });
    msgDiv.scrollTop = msgDiv.scrollHeight;
}

async function sendPrompt() {
    const promptInput = document.getElementById("prompt");
    const prompt = promptInput.value;
    if (!prompt || !currentChatId) return;

    const userDiv = document.createElement("div");
    userDiv.className = "user";
    userDiv.textContent = prompt;
    document.getElementById("messages").appendChild(userDiv);
    promptInput.value = "";

    const res = await fetch("/send", {
        method: "POST",
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ chat_id: currentChatId, prompt })
    });

    const data = await res.json();
    const aiDiv = document.createElement("div");
    aiDiv.className = "ai";
    aiDiv.textContent = data.reply;
    document.getElementById("messages").appendChild(aiDiv);
    document.getElementById("messages").scrollTop = document.getElementById("messages").scrollHeight;
}

// Helper function to append and format messages
function appendMessage(role, text, messageIndex) { // Added messageIndex
    const msgDiv = document.getElementById("messages");
    const div = document.createElement("div");
    div.className = role;
    div.dataset.messageIndex = messageIndex; // Store index for editing

    if (role === 'ai') {
        div.innerHTML = formatMessage(text);

        // Add copy button to AI messages and code blocks
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-button';
        copyButton.textContent = 'Copy';
        copyButton.onclick = () => copyMessage(text);
        div.appendChild(copyButton);

        // Add copy buttons to code blocks within the message
        div.querySelectorAll('pre code').forEach(codeBlock => {
            const codeCopyButton = document.createElement('button');
            codeCopyButton.className = 'copy-button';
            codeCopyButton.textContent = 'Copy';
            codeCopyButton.onclick = () => copyMessage(codeBlock.textContent);
            // Position the button relative to the pre element
            codeBlock.parentElement.style.position = 'relative';
            codeBlock.parentElement.appendChild(codeCopyButton);
        });

    } else { // User message
        div.textContent = text;

        // Add edit button to user messages on hover
        const editButton = document.createElement('button');
        editButton.className = 'edit-button';
        editButton.textContent = 'Edit';
        editButton.onclick = () => editMessage(messageIndex, text);
        div.appendChild(editButton);
    }

    msgDiv.appendChild(div);
}

// Function to format messages (enhanced)
function formatMessage(text) {
    let formattedText = text;

    // Escape HTML characters to prevent XSS and ensure markdown is parsed correctly
    formattedText = formattedText.replace(/&/g, '&')
                               .replace(/</g, '<')
                               .replace(/>/g, '>')
                               .replace(/"/g, '"')
                               .replace(/'/g, '&#039;');

    // Code blocks (triple backticks) - needs to be done before other replacements
    // Use a replacer function to prevent re-escaping already escaped HTML within code blocks
    formattedText = formattedText.replace(/```(\w+)?\n([\s\S]*?)\n```/g, (match, lang, code) => {
        // Unescape HTML characters within the code block content
        code = code.replace(/&/g, '&')
                   .replace(/</g, '<')
                   .replace(/>/g, '>')
                   .replace(/"/g, '"')
                   .replace(/&#039;/g, "'");
        return `<pre><code class="language-${lang || ''}">${code}</code></pre>`;
    });

    // Inline code (single backticks)
    formattedText = formattedText.replace(/`([^`]+)`/g, '<code>$1</code>');

    // Bold (**text**)
    formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Italic (*text* or _text_)
    formattedText = formattedText.replace(/\*(.*?)\*/g, '<em>$1</em>');
    formattedText = formattedText.replace(/_(.*?)_/g, '<em>$1</em>');

    // Basic lists (unordered and ordered)
    // This is a more robust list formatting that handles multiple list items
    const lines = formattedText.split('\n');
    let inList = false;
    let listType = '';
    let processedLines = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const trimmedLine = line.trim();

        if (trimmedLine.match(/^[-*+]\s/)) { // Unordered list item
            if (!inList || listType !== 'ul') {
                if (inList) processedLines.push(`</${listType}>`);
                processedLines.push('<ul>');
                inList = true;
                listType = 'ul';
            }
            processedLines.push(`<li>${trimmedLine.substring(trimmedLine.indexOf(' ') + 1)}</li>`);
        } else if (trimmedLine.match(/^\d+\.\s/)) { // Ordered list item
            if (!inList || listType !== 'ol') {
                if (inList) processedLines.push(`</${listType}>`);
                processedLines.push('<ol>');
                inList = true;
                listType = 'ol';
            }
            processedLines.push(`<li>${trimmedLine.substring(trimmedLine.indexOf(' ') + 1)}</li>`);
        } else {
            if (inList) {
                processedLines.push(`</${listType}>`);
                inList = false;
            }
            processedLines.push(line);
        }
    }
    if (inList) {
        processedLines.push(`</${listType}>`);
    }

    formattedText = processedLines.join('\n');

    // Convert double newlines to paragraph breaks, single newlines to <br>
    formattedText = formattedText.replace(/\n\n/g, '</p><p>');
    formattedText = formattedText.replace(/\n/g, '<br>');
    formattedText = `<p>${formattedText}</p>`; // Wrap in initial paragraph

    return formattedText;
}

// Function to copy message text to clipboard
function copyMessage(text) {
    navigator.clipboard.writeText(text).then(() => {
        console.log("Text copied to clipboard");
        // Optional: Show a temporary "Copied!" message
    }).catch(err => {
        console.error("Failed to copy text: ", err);
    });
}

// Function to edit user message
function editMessage(messageIndex, currentText) {
    const messagesDiv = document.getElementById('messages');
    const messageElement = messagesDiv.querySelector(`.user[data-message-index="${messageIndex}"]`);
    if (!messageElement) return;

    // Replace message content with a textarea for editing
    messageElement.innerHTML = `
        <textarea class="edit-textarea">${currentText}</textarea>
        <button class="save-edit-button" onclick="saveEditedMessage(${messageIndex})">Save</button>
        <button class="cancel-edit-button" onclick="cancelEditMessage(${messageIndex}, '${currentText.replace(/'/g, "\\'")}')">Cancel</button>
    `;

    const textarea = messageElement.querySelector('.edit-textarea');
    textarea.focus();
    // Adjust textarea height
    textarea.style.height = 'auto';
    textarea.style.height = (textarea.scrollHeight) + 'px';
    textarea.addEventListener('input', () => {
        textarea.style.height = 'auto';
        textarea.style.height = (textarea.scrollHeight) + 'px';
    });
}

// Function to save edited message and resend
async function saveEditedMessage(messageIndex) {
    const messagesDiv = document.getElementById('messages');
    const messageElement = messagesDiv.querySelector(`.user[data-message-index="${messageIndex}"]`);
    if (!messageElement) return;

    const textarea = messageElement.querySelector('.edit-textarea');
    const newText = textarea.value;

    // Here you would typically update the message on the backend and resend the prompt
    // For this example, we'll just update the UI and simulate resending
    console.log(`Saving edited message ${messageIndex}: ${newText}`);

    // Remove the old message and subsequent AI messages
    let nextElement = messageElement.nextElementSibling;
    while (nextElement) {
        const toRemove = nextElement;
        nextElement = nextElement.nextElementSibling;
        toRemove.remove();
    }
    messageElement.remove();


    // Simulate resending the prompt
    document.getElementById('prompt').value = newText;
    sendPrompt();

    // Note: A more robust solution would involve sending the edited message and chat history
    // to the backend to regenerate the response from that point.
}

// Function to cancel editing
function cancelEditMessage(messageIndex, originalText) {
     const messagesDiv = document.getElementById('messages');
    const messageElement = messagesDiv.querySelector(`.user[data-message-index="${messageIndex}"]`);
    if (!messageElement) return;

    // Restore original message content
    messageElement.innerHTML = ''; // Clear current content
    messageElement.textContent = originalText; // Restore text

    // Re-add the edit button
    const editButton = document.createElement('button');
    editButton.className = 'edit-button';
    editButton.textContent = 'Edit';
    editButton.onclick = () => editMessage(messageIndex, originalText);
    messageElement.appendChild(editButton);
}


// Placeholder functions for rename and delete (will implement logic later)
function promptRenameChat(chatId, currentTitle) {
    const newTitle = prompt("Enter new title for the chat:", currentTitle);
    if (newTitle && newTitle !== currentTitle) {
        renameChat(chatId, newTitle);
    }
}

async function renameChat(chatId, newTitle) {
    console.log(`Attempting to rename chat ${chatId} to ${newTitle}`);
    // Implement fetch call to backend rename endpoint here
    const res = await fetch(`/rename_chat/${chatId}`, {
        method: "POST",
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title: newTitle })
    });
    const data = await res.json();
    if (data.success) {
        console.log("Rename successful");
        // Update the title in the sidebar and the current chat title area if it's the active chat
        loadChats(); // Reload chats to update sidebar
        if (currentChatId === chatId) {
            currentChatTitle = newTitle;
            document.getElementById("chatTitleArea").textContent = currentChatTitle;
        }
    } else {
        console.error("Rename failed:", data.message);
        alert("Failed to rename chat: " + data.message);
    }
}

async function deleteChat(chatId) {
    if (confirm("Are you sure you want to delete this chat?")) {
        console.log(`Attempting to delete chat ${chatId}`);
        // Implement fetch call to backend delete endpoint here
        const res = await fetch(`/delete_chat/${chatId}`, {
            method: "DELETE"
        });
        const data = await res.json();
        if (data.success) {
            console.log("Delete successful");
            loadChats(); // Reload chats to update sidebar
            // If the deleted chat was the current one, start a new chat
            if (currentChatId === chatId) {
                startNewChat();
            }
        } else {
            console.error("Delete failed:", data.message);
            alert("Failed to delete chat: " + data.message);
        }
    }
}


window.onload = () => {
    startNewChat();
    loadChats();
};
