let currentChatId = null;
let currentChatTitle = "New Chat"; // Variable to store the current chat title
let isInitialized = false; // Track if app has been initialized

async function loadChats() {
    const res = await fetch("/chats");
    const chats = await res.json();
    const list = document.getElementById("chatList");
    list.innerHTML = "";

    // Sort chats by creation time (newest first)
    chats.sort((a, b) => b.id.localeCompare(a.id));

    chats.forEach(chat => {
        const chatItem = document.createElement("div");
        chatItem.className = "chat-item"; // Add a class for styling
        chatItem.dataset.chatId = chat.id; // Store chat ID on the element

        const chatButton = document.createElement("button");
        chatButton.textContent = chat.title;
        chatButton.onclick = () => loadChat(chat.id, chat.title); // Pass title to loadChat
        chatButton.ondblclick = (e) => { // Handle double-click for rename
            e.preventDefault(); // Prevent text selection on double click
            promptRenameChat(chat.id, chat.title);
        };
        chatButton.oncontextmenu = (e) => { // Handle right-click for delete
            e.preventDefault(); // Prevent default context menu
            showChatContextMenu(e, chat.id);
        };
        chatItem.appendChild(chatButton);

        list.appendChild(chatItem);
    });

    return chats;
}

// Function to show context menu for chat items
function showChatContextMenu(event, chatId) {
    // Remove any existing context menus
    const existingMenu = document.getElementById('chatContextMenu');
    if (existingMenu) {
        existingMenu.remove();
    }

    const contextMenu = document.createElement('div');
    contextMenu.id = 'chatContextMenu';
    contextMenu.className = 'context-menu';
    contextMenu.style.top = `${event.clientY}px`;
    contextMenu.style.left = `${event.clientX}px`;

    const deleteOption = document.createElement('div');
    deleteOption.className = 'context-menu-item';
    deleteOption.textContent = 'Delete Chat';
    deleteOption.onclick = () => {
        deleteChat(chatId);
        contextMenu.remove(); // Close menu after action
    };
    contextMenu.appendChild(deleteOption);

    document.body.appendChild(contextMenu);

    // Close menu when clicking outside
    document.addEventListener('click', function closeMenu(e) {
        if (!contextMenu.contains(e.target)) {
            contextMenu.remove();
            document.removeEventListener('click', closeMenu);
        }
    });
}


async function startNewChat() {
    // Check if there's already an empty chat
    const chats = await loadChats();
    const emptyChat = chats.find(chat => chat.title === "New Chat" || chat.isEmpty);

    if (emptyChat) {
        // Load the existing empty chat instead of creating a new one
        loadChat(emptyChat.id, emptyChat.title);
        return;
    }

    // Create new chat only if no empty chat exists
    const res = await fetch("/new_chat", { method: "POST" });
    const data = await res.json();
    currentChatId = data.chat_id;
    currentChatTitle = "New Chat";

    // Clear messages and reset title
    const messagesDiv = document.getElementById("messages");
    messagesDiv.innerHTML = "";
    document.getElementById("chatTitleArea").textContent = currentChatTitle;

    loadChats();
}

// Function to clean up event listeners and prevent memory leaks
function cleanupEventListeners() {
    const messagesDiv = document.getElementById("messages");
    if (messagesDiv) {
        // Remove all event listeners by cloning the node
        const newMessagesDiv = messagesDiv.cloneNode(false);
        messagesDiv.parentNode.replaceChild(newMessagesDiv, messagesDiv);
    }
}

async function loadChat(chatId, chatTitle) {
    const res = await fetch(`/chat/${chatId}`);
    const messages = await res.json();
    currentChatId = chatId;
    currentChatTitle = chatTitle; // Set the current chat title
    document.getElementById("chatTitleArea").textContent = currentChatTitle; // Display the title

    // Clean up existing event listeners
    cleanupEventListeners();

    const msgDiv = document.getElementById("messages");
    msgDiv.innerHTML = "";

    messages.forEach((msg, index) => { // Pass index to appendMessage
        appendMessage(msg.role, msg.text, index); // Use a helper function for appending and formatting
    });

    // Scroll to bottom
    msgDiv.scrollTop = msgDiv.scrollHeight;
}

async function sendPrompt() {
    const promptInput = document.getElementById("prompt");
    const prompt = promptInput.value.trim();
    if (!prompt || !currentChatId) return;

    const messagesDiv = document.getElementById("messages");

    // Get current message count for indexing
    const currentMessageCount = messagesDiv.children.length;

    // Add user message using appendMessage function
    appendMessage("user", prompt, currentMessageCount);
    promptInput.value = "";

    // Scroll to bottom
    messagesDiv.scrollTop = messagesDiv.scrollHeight;

    try {
        const res = await fetch("/send", {
            method: "POST",
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ chat_id: currentChatId, prompt })
        });

        if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
        }

        const data = await res.json();

        // Add AI response using appendMessage function
        appendMessage("ai", data.reply, currentMessageCount + 1);

        // Scroll to bottom
        messagesDiv.scrollTop = messagesDiv.scrollHeight;

    } catch (error) {
        console.error("Error sending prompt:", error);

        // Add error message
        const errorDiv = document.createElement("div");
        errorDiv.className = "ai error";
        errorDiv.textContent = "Sorry, there was an error processing your request. Please try again.";
        errorDiv.style.backgroundColor = "#dc3545";
        messagesDiv.appendChild(errorDiv);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }
}

// Helper function to append and format messages
function appendMessage(role, text, messageIndex) { // Added messageIndex
    const msgDiv = document.getElementById("messages");
    const div = document.createElement("div");
    div.className = role;
    div.dataset.messageIndex = messageIndex; // Store index for editing
    div.dataset.processed = 'true'; // Mark as processed to prevent re-processing

    if (role === 'ai') {
        // Set position relative for proper button positioning
        div.style.position = 'relative';

        // Always format the message, but ensure it's done correctly
        const formattedContent = formatMessage(text);
        div.innerHTML = formattedContent;

        // Add single copy button for the entire AI message
        addCopyButton(div, text, 'message');

        // Add copy buttons to code blocks within the message
        setTimeout(() => {
            div.querySelectorAll('pre').forEach(preElement => {
                const codeBlock = preElement.querySelector('code');
                if (codeBlock) {
                    preElement.style.position = 'relative';
                    addCopyButton(preElement, codeBlock.textContent, 'code');
                }
            });
        }, 10); // Small delay to ensure DOM is ready

    } else { // User message
        div.style.position = 'relative';
        div.textContent = text;

        // Add edit button to user messages
        addEditButton(div, messageIndex, text);
    }

    msgDiv.appendChild(div);

    // Add entrance animation
    div.style.opacity = '0';
    div.style.transform = 'translateY(20px)';
    setTimeout(() => {
        div.style.transition = 'all 0.3s ease-out';
        div.style.opacity = '1';
        div.style.transform = 'translateY(0)';
    }, 10);
}

// Function to add copy button with proper cleanup
function addCopyButton(container, textToCopy, type) {
    // Remove existing copy button if any
    const existingButton = container.querySelector('.copy-button');
    if (existingButton) {
        existingButton.remove();
    }

    const copyButton = document.createElement('button');
    copyButton.className = `copy-button copy-button-${type}`;
    copyButton.textContent = 'Copy';
    copyButton.setAttribute('data-text', textToCopy);

    // Use event delegation to avoid multiple listeners
    copyButton.addEventListener('click', function(e) {
        e.stopPropagation();
        copyMessage(this.getAttribute('data-text'));
        showCopyFeedback(this);
    });

    container.appendChild(copyButton);
}

// Function to add edit button with proper cleanup
function addEditButton(container, messageIndex, text) {
    // Remove existing edit button if any
    const existingButton = container.querySelector('.edit-button');
    if (existingButton) {
        existingButton.remove();
    }

    const editButton = document.createElement('button');
    editButton.className = 'edit-button';
    editButton.textContent = 'Edit';
    editButton.addEventListener('click', function(e) {
        e.stopPropagation();
        editMessage(messageIndex, text);
    });

    container.appendChild(editButton);
}

// Function to format messages (enhanced and stabilized)
function formatMessage(text) {
    // Always process the text to ensure consistent formatting
    let formattedText = text;

    // Escape HTML characters to prevent XSS and ensure markdown is parsed correctly
    formattedText = formattedText.replace(/&/g, '&amp;')
                               .replace(/</g, '&lt;')
                               .replace(/>/g, '&gt;')
                               .replace(/"/g, '&quot;')
                               .replace(/'/g, '&#039;');

    // Code blocks (triple backticks) - needs to be done before other replacements
    // Use a replacer function to prevent re-escaping already escaped HTML within code blocks
    formattedText = formattedText.replace(/```(\w+)?\n?([\s\S]*?)\n?```/g, (_, lang, code) => {
        // Unescape HTML characters within the code block content
        code = code.replace(/&amp;/g, '&')
                   .replace(/&lt;/g, '<')
                   .replace(/&gt;/g, '>')
                   .replace(/&quot;/g, '"')
                   .replace(/&#039;/g, "'");
        return `<pre><code class="language-${lang || ''}">${code}</code></pre>`;
    });

    // Inline code (single backticks) - avoid processing if inside code blocks
    formattedText = formattedText.replace(/`([^`\n]+)`/g, '<code>$1</code>');

    // Bold (**text**) - avoid processing if inside code blocks
    formattedText = formattedText.replace(/\*\*([^*\n]+)\*\*/g, '<strong>$1</strong>');

    // Italic (*text* or _text_) - avoid processing if inside code blocks
    formattedText = formattedText.replace(/\*([^*\n]+)\*/g, '<em>$1</em>');
    formattedText = formattedText.replace(/_([^_\n]+)_/g, '<em>$1</em>');

    // Process lists more carefully
    const lines = formattedText.split('\n');
    let inList = false;
    let listType = '';
    let processedLines = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const trimmedLine = line.trim();

        // Skip processing if we're inside a code block
        if (line.includes('<pre>') || line.includes('</pre>')) {
            if (inList) {
                processedLines.push(`</${listType}>`);
                inList = false;
            }
            processedLines.push(line);
            continue;
        }

        if (trimmedLine.match(/^[-*+]\s/)) { // Unordered list item
            if (!inList || listType !== 'ul') {
                if (inList) processedLines.push(`</${listType}>`);
                processedLines.push('<ul>');
                inList = true;
                listType = 'ul';
            }
            processedLines.push(`<li>${trimmedLine.substring(trimmedLine.indexOf(' ') + 1)}</li>`);
        } else if (trimmedLine.match(/^\d+\.\s/)) { // Ordered list item
            if (!inList || listType !== 'ol') {
                if (inList) processedLines.push(`</${listType}>`);
                processedLines.push('<ol>');
                inList = true;
                listType = 'ol';
            }
            processedLines.push(`<li>${trimmedLine.substring(trimmedLine.indexOf(' ') + 1)}</li>`);
        } else {
            if (inList) {
                processedLines.push(`</${listType}>`);
                inList = false;
            }
            processedLines.push(line);
        }
    }
    if (inList) {
        processedLines.push(`</${listType}>`);
    }

    formattedText = processedLines.join('\n');

    // Convert double newlines to paragraph breaks, single newlines to <br>
    // But avoid processing inside code blocks
    const parts = formattedText.split(/(<pre>[\s\S]*?<\/pre>)/);
    for (let i = 0; i < parts.length; i++) {
        if (!parts[i].startsWith('<pre>')) {
            parts[i] = parts[i].replace(/\n\n+/g, '</p><p>');
            parts[i] = parts[i].replace(/\n/g, '<br>');
        }
    }
    formattedText = parts.join('');

    // Wrap in paragraph tags if not already wrapped
    if (!formattedText.startsWith('<p>') && !formattedText.startsWith('<pre>') && !formattedText.startsWith('<ul>') && !formattedText.startsWith('<ol>')) {
        formattedText = `<p>${formattedText}</p>`;
    }

    return formattedText;
}

// Function to copy message text to clipboard
function copyMessage(text) {
    // Clean up the text by removing HTML tags and unescaping entities
    const cleanText = text.replace(/<[^>]*>/g, '')
                         .replace(/&amp;/g, '&')
                         .replace(/&lt;/g, '<')
                         .replace(/&gt;/g, '>')
                         .replace(/&quot;/g, '"')
                         .replace(/&#039;/g, "'")
                         .replace(/<br>/g, '\n')
                         .replace(/<\/p><p>/g, '\n\n');

    navigator.clipboard.writeText(cleanText).then(() => {
        console.log("Text copied to clipboard");
    }).catch(err => {
        console.error("Failed to copy text: ", err);
        // Fallback for older browsers
        fallbackCopyTextToClipboard(cleanText);
    });
}

// Fallback copy function for older browsers
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        console.log('Fallback: Text copied to clipboard');
    } catch (err) {
        console.error('Fallback: Unable to copy text', err);
    }

    document.body.removeChild(textArea);
}

// Function to show copy feedback
function showCopyFeedback(button) {
    const originalText = button.textContent;
    button.textContent = 'Copied!';
    button.style.backgroundColor = '#28a745';

    setTimeout(() => {
        button.textContent = originalText;
        button.style.backgroundColor = '';
    }, 1500);
}

// Function to edit user message
function editMessage(messageIndex, currentText) {
    const messagesDiv = document.getElementById('messages');
    const messageElement = messagesDiv.querySelector(`.user[data-message-index="${messageIndex}"]`);
    if (!messageElement) return;

    // Replace message content with a textarea for editing
    messageElement.innerHTML = `
        <textarea class="edit-textarea">${currentText}</textarea>
        <button class="save-edit-button" onclick="saveEditedMessage(${messageIndex})">Save</button>
        <button class="cancel-edit-button" onclick="cancelEditMessage(${messageIndex}, '${currentText.replace(/'/g, "\\'")}')">Cancel</button>
    `;

    const textarea = messageElement.querySelector('.edit-textarea');
    textarea.focus();
    // Adjust textarea height
    textarea.style.height = 'auto';
    textarea.style.height = (textarea.scrollHeight) + 'px';
    textarea.addEventListener('input', () => {
        textarea.style.height = 'auto';
        textarea.style.height = (textarea.scrollHeight) + 'px';
    });
}

// Function to save edited message and resend
async function saveEditedMessage(messageIndex) {
    const messagesDiv = document.getElementById('messages');
    const messageElement = messagesDiv.querySelector(`.user[data-message-index="${messageIndex}"]`);
    if (!messageElement) return;

    const textarea = messageElement.querySelector('.edit-textarea');
    const newText = textarea.value;

    // Here you would typically update the message on the backend and resend the prompt
    // For this example, we'll just update the UI and simulate resending
    console.log(`Saving edited message ${messageIndex}: ${newText}`);

    // Remove the old message and subsequent AI messages
    let nextElement = messageElement.nextElementSibling;
    while (nextElement) {
        const toRemove = nextElement;
        nextElement = nextElement.nextElementSibling;
        toRemove.remove();
    }
    messageElement.remove();


    // Simulate resending the prompt
    document.getElementById('prompt').value = newText;
    sendPrompt();

    // Note: A more robust solution would involve sending the edited message and chat history
    // to the backend to regenerate the response from that point.
}

// Function to cancel editing
function cancelEditMessage(messageIndex, originalText) {
     const messagesDiv = document.getElementById('messages');
    const messageElement = messagesDiv.querySelector(`.user[data-message-index="${messageIndex}"]`);
    if (!messageElement) return;

    // Restore original message content
    messageElement.innerHTML = ''; // Clear current content
    messageElement.textContent = originalText; // Restore text

    // Re-add the edit button
    const editButton = document.createElement('button');
    editButton.className = 'edit-button';
    editButton.textContent = 'Edit';
    editButton.onclick = () => editMessage(messageIndex, originalText);
    messageElement.appendChild(editButton);
}


// Placeholder functions for rename and delete (will implement logic later)
function promptRenameChat(chatId, currentTitle) {
    const newTitle = prompt("Enter new title for the chat:", currentTitle);
    if (newTitle && newTitle !== currentTitle) {
        renameChat(chatId, newTitle);
    }
}

async function renameChat(chatId, newTitle) {
    console.log(`Attempting to rename chat ${chatId} to ${newTitle}`);
    // Implement fetch call to backend rename endpoint here
    const res = await fetch(`/rename_chat/${chatId}`, {
        method: "POST",
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title: newTitle })
    });
    const data = await res.json();
    if (data.success) {
        console.log("Rename successful");
        // Update the title in the sidebar and the current chat title area if it's the active chat
        loadChats(); // Reload chats to update sidebar
        if (currentChatId === chatId) {
            currentChatTitle = newTitle;
            document.getElementById("chatTitleArea").textContent = currentChatTitle;
        }
    } else {
        console.error("Rename failed:", data.message);
        alert("Failed to rename chat: " + data.message);
    }
}

async function deleteChat(chatId) {
    if (confirm("Are you sure you want to delete this chat?")) {
        console.log(`Attempting to delete chat ${chatId}`);
        // Implement fetch call to backend delete endpoint here
        const res = await fetch(`/delete_chat/${chatId}`, {
            method: "DELETE"
        });
        const data = await res.json();
        if (data.success) {
            console.log("Delete successful");
            loadChats(); // Reload chats to update sidebar
            // If the deleted chat was the current one, start a new chat
            if (currentChatId === chatId) {
                startNewChat();
            }
        } else {
            console.error("Delete failed:", data.message);
            alert("Failed to delete chat: " + data.message);
        }
    }
}


window.onload = async () => {
    if (!isInitialized) {
        isInitialized = true;

        // Load existing chats first
        const chats = await loadChats();

        // Check if there's an existing empty chat
        const emptyChat = chats.find(chat => chat.isEmpty);

        if (emptyChat) {
            // Load the existing empty chat
            loadChat(emptyChat.id, emptyChat.title);
        } else if (chats.length > 0) {
            // Load the most recent chat
            loadChat(chats[0].id, chats[0].title);
        } else {
            // Create a new chat only if no chats exist
            startNewChat();
        }
    }
};
