<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Nexus</title>
    <link rel="stylesheet" href="/static/style.css" />
    <link rel="icon" href="/static/logo.ico" type="image/x-icon">
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <button class="new-chat-btn" onclick="startNewChat()">+ New Chat</button>
            <div class="chat-list" id="chatList">
                <!-- Chat titles will be loaded here by script.js -->
            </div>
        </div>
        <div class="main">
            <div class="chat-title-area" id="chatTitleArea">
                <!-- Current chat title will be displayed here by script.js -->
            </div>
            <div class="messages" id="messages">
                <!-- Messages will be loaded here by script.js -->
            </div>
            <div class="input-area">
                <input type="text" id="prompt" placeholder="Send a message..." onkeydown="if(event.key === 'Enter') sendPrompt()" />
                <button onclick="sendPrompt()">➤</button>
            </div>
        </div>
    </div>
    <script src="/static/script.js"></script>
</body>
</html>
