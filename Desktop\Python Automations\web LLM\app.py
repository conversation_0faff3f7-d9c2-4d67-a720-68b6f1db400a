from flask import Flask, request, jsonify, render_template
import os, uuid, json
import google.generativeai as genai

# Configure Gemini
genai.configure(api_key="AIzaSyDzVcofu-epzmPwqqGugQeT7Hox5G2Qk1k")
model = genai.GenerativeModel("models/gemma-3-27b-it")

app = Flask(__name__)
CHAT_DIR = "chats"
os.makedirs(CHAT_DIR, exist_ok=True)

# Route: Main Page
@app.route("/")
def index():
    return render_template("index.html")

# Route: Create New Chat
@app.route("/new_chat", methods=["POST"])
def new_chat():
    chat_id = f"chat_{uuid.uuid4().hex[:8]}"
    filepath = os.path.join(CHAT_DIR, f"{chat_id}.json")
    with open(filepath, "w") as f:
        json.dump({"id": chat_id, "messages": []}, f)
    return jsonify({"chat_id": chat_id})

# Route: Send Message
@app.route("/send", methods=["POST"])
def send():
    data = request.json
    chat_id = data["chat_id"]
    prompt = data["prompt"]
    filepath = os.path.join(CHAT_DIR, f"{chat_id}.json")

    with open(filepath) as f:
        chat = json.load(f)

    reply = model.generate_content(prompt).text
    chat["messages"].append({"role": "user", "text": prompt})
    chat["messages"].append({"role": "ai", "text": reply})

    with open(filepath, "w") as f:
        json.dump(chat, f)

    return jsonify({"reply": reply})

# Route: Load Chat Titles
@app.route("/chats")
def list_chats():
    chats = []
    for fname in os.listdir(CHAT_DIR):
        with open(os.path.join(CHAT_DIR, fname)) as f:
            chat = json.load(f)
            title = chat["messages"][0]["text"][:20] if chat["messages"] else "New Chat"
            chats.append({"id": chat["id"], "title": title})
    return jsonify(chats)

# Route: Load Specific Chat
@app.route("/chat/<chat_id>")
def get_chat(chat_id):
    filepath = os.path.join(CHAT_DIR, f"{chat_id}.json")
    with open(filepath) as f:
        chat = json.load(f)
    return jsonify(chat["messages"])

# Route: Rename Chat
@app.route("/rename_chat/<chat_id>", methods=["POST"])
def rename_chat(chat_id):
    data = request.json
    new_title = data["title"]
    filepath = os.path.join(CHAT_DIR, f"{chat_id}.json")

    try:
        with open(filepath, "r+") as f:
            chat = json.load(f)
            # Assuming the first user message is the de facto title
            if chat["messages"]:
                chat["messages"][0]["text"] = new_title
            else:
                 # Handle case where chat is empty, maybe add a system message or just store the title
                 # For now, let's just add a placeholder if empty
                 chat["messages"].insert(0, {"role": "system", "text": new_title})

            f.seek(0)
            json.dump(chat, f, indent=4)
            f.truncate()
        return jsonify({"success": True, "message": "Chat renamed successfully"})
    except FileNotFoundError:
        return jsonify({"success": False, "message": "Chat not found"}), 404
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

# Route: Delete Chat
@app.route("/delete_chat/<chat_id>", methods=["DELETE"])
def delete_chat(chat_id):
    filepath = os.path.join(CHAT_DIR, f"{chat_id}.json")

    try:
        os.remove(filepath)
        return jsonify({"success": True, "message": "Chat deleted successfully"})
    except FileNotFoundError:
        return jsonify({"success": False, "message": "Chat not found"}), 404
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

if __name__ == "__main__":
    app.run(debug=True)
