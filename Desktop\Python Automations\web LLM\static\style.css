body {
    margin: 0;
    font-family: 'Segoe UI', sans-serif;
    background-color: #121212;
    color: #e0e0e0;
}

.container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

.sidebar {
    width: 260px;
    background-color: #1e1e1e;
    border-right: 1px solid #333;
    padding: 1rem;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.new-chat-btn {
    background-color: #3f3f3f;
    color: white;
    padding: 10px;
    border: none;
    border-radius: 5px;
    margin-bottom: 1rem;
    cursor: pointer;
    font-weight: bold;
}

.new-chat-btn:hover {
    background-color: #575757;
}

.chat-list {
    flex: 1;
    overflow-y: auto;
}

.chat-list button {
    background: none;
    border: none;
    color: #bbb;
    text-align: left;
    padding: 10px;
    width: 100%;
    border-radius: 5px;
    cursor: pointer;
    flex-grow: 1; /* Allow button to take available space */
    overflow: hidden; /* Hide overflow text */
    text-overflow: ellipsis; /* Add ellipsis for overflow */
    white-space: nowrap; /* Prevent wrapping */
}

.chat-list button:hover {
    background-color: #333;
    color: white;
}

.chat-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    position: relative; /* Needed for context menu positioning */
    transition: background-color 0.2s ease; /* Smooth transition for hover */
}

.chat-item:hover {
    background-color: #282828; /* Slightly darker on hover for the whole item */
}

/* Context Menu Styles */
.context-menu {
    position: fixed;
    background-color: #2c2c2e;
    border: 1px solid #333;
    border-radius: 5px;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.5);
    z-index: 1000;
    padding: 5px 0;
}

.context-menu-item {
    padding: 8px 15px;
    cursor: pointer;
    color: #e0e0e0;
}

.context-menu-item:hover {
    background-color: #3f3f3f;
}


.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #121212;
}

.chat-title-area {
    padding: 1rem;
    font-size: 1.2rem;
    font-weight: bold;
    border-bottom: 1px solid #333;
    text-align: center;
}

.messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Message animation */
.messages .user,
.messages .ai {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInSlideUp 0.3s ease-out forwards;
}

@keyframes fadeInSlideUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.messages .user {
    align-self: flex-end;
    background-color: #0a84ff;
    padding: 10px;
    border-radius: 10px;
    max-width: 70%;
    color: white;
}

.messages .ai {
    align-self: flex-start;
    background-color: #2c2c2e;
    padding: 10px;
    border-radius: 10px;
    max-width: 70%;
    color: #e0e0e0;
    white-space: pre-wrap; /* Preserve whitespace and line breaks */
    word-wrap: break-word; /* Break long words */
}

.messages .ai code {
    background-color: #333;
    padding: 2px 4px;
    border-radius: 4px;
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
}

.messages .ai pre {
    background-color: #333;
    padding: 10px;
    border-radius: 8px;
    overflow-x: auto;
}

.messages .ai pre code {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
}

/* Copy button styles */
.copy-button {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 3px 6px;
    cursor: pointer;
    font-size: 0.8em;
    opacity: 0; /* Hidden by default */
    transition: opacity 0.2s ease-in-out;
}

.messages .ai:hover .copy-button {
    opacity: 1; /* Show on hover */
}

.messages .ai pre:hover .copy-button {
     opacity: 1; /* Show on hover for code blocks */
}

/* Edit button styles */
.edit-button {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 3px 6px;
    cursor: pointer;
    font-size: 0.8em;
    opacity: 0; /* Hidden by default */
    transition: opacity 0.2s ease-in-out;
}

.messages .user:hover .edit-button {
    opacity: 1; /* Show on hover */
}


.input-area {
    display: flex;
    padding: 10px;
    border-top: 1px solid #333;
    background-color: #1c1c1e;
}

.input-area input {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 8px;
    background-color: #2c2c2e;
    color: white;
    font-size: 16px;
}

.input-area button {
    margin-left: 10px;
    padding: 12px;
    background-color: #0a84ff;
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    cursor: pointer;
}

.input-area button:hover {
    background-color: #006ce0;
}
