* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Robot<PERSON>, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
    color: #e8e8e8;
    overflow: hidden;
}

.container {
    display: flex;
    height: 100vh;
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.sidebar {
    width: 280px;
    min-width: 200px;
    max-width: 500px;
    background: rgba(20, 20, 20, 0.95);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    transition: width 0.1s ease-out;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(64, 224, 208, 0.03), rgba(255, 0, 150, 0.03));
    pointer-events: none;
}

/* Resize handle */
.resize-handle {
    width: 4px;
    background: rgba(255, 255, 255, 0.1);
    cursor: col-resize;
    position: relative;
    transition: all 0.2s ease;
    flex-shrink: 0;
    border-left: 1px solid rgba(255, 255, 255, 0.05);
    border-right: 1px solid rgba(255, 255, 255, 0.05);
}

.resize-handle:hover {
    background: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
}

.resize-handle:active {
    background: rgba(102, 126, 234, 0.8);
    box-shadow: 0 0 15px rgba(102, 126, 234, 0.5);
}

.resize-handle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -4px;
    right: -4px;
    bottom: 0;
    background: transparent;
}

/* Add a subtle indicator in the middle of the resize handle */
.resize-handle::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 30px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 1px;
    transition: all 0.2s ease;
}

.resize-handle:hover::after {
    background: rgba(255, 255, 255, 0.6);
    height: 40px;
}

.new-chat-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 14px 20px;
    border: none;
    border-radius: 16px;
    margin-bottom: 1.5rem;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.new-chat-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.new-chat-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.new-chat-btn:hover::before {
    left: 100%;
}

.new-chat-btn:active {
    transform: translateY(0);
}

.chat-list {
    flex: 1;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.chat-list::-webkit-scrollbar {
    width: 6px;
}

.chat-list::-webkit-scrollbar-track {
    background: transparent;
}

.chat-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.chat-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

.chat-list button {
    background: rgba(255, 255, 255, 0.05);
    border: none;
    color: #c0c0c0;
    text-align: left;
    padding: 12px 16px;
    width: 100%;
    border-radius: 12px;
    cursor: pointer;
    flex-grow: 1;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.chat-list button:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.chat-title {
    display: block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    outline: none;
}

.chat-title.editing {
    background: rgba(102, 126, 234, 0.2);
    border-radius: 4px;
    padding: 2px 4px;
    margin: -2px -4px;
    white-space: normal;
    word-wrap: break-word;
    border: 1px solid rgba(102, 126, 234, 0.5);
}

.chat-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.chat-item:hover {
    transform: scale(1.02);
}

/* Context Menu Styles */
.context-menu {
    position: fixed;
    background: rgba(30, 30, 30, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    z-index: 1000;
    padding: 8px;
    animation: contextMenuAppear 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes contextMenuAppear {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.context-menu-item {
    padding: 10px 16px;
    cursor: pointer;
    color: #e0e0e0;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.context-menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(2px);
}


.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(15, 15, 15, 0.8);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.chat-title-area {
    padding: 1.5rem;
    font-size: 1.3rem;
    font-weight: 700;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 1;
}

.messages {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 16px;
    position: relative;
    z-index: 1;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.messages::-webkit-scrollbar {
    width: 8px;
}

.messages::-webkit-scrollbar-track {
    background: transparent;
}

.messages::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
}

.messages::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Message animation */
.messages .user,
.messages .ai {
    animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.messages .user {
    align-self: flex-end;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 16px 20px;
    border-radius: 20px 20px 6px 20px;
    max-width: 75%;
    color: white;
    position: relative;
    word-wrap: break-word;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: 500;
}

.messages .ai {
    align-self: flex-start;
    background: rgba(30, 30, 30, 0.8);
    padding: 16px 20px;
    border-radius: 20px 20px 20px 6px;
    max-width: 75%;
    color: #e8e8e8;
    white-space: pre-wrap;
    word-wrap: break-word;
    position: relative;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Ensure code blocks have proper positioning */
.messages .ai pre {
    position: relative;
    margin: 8px 0;
}

/* Improve list styling */
.messages .ai ul, .messages .ai ol {
    margin: 8px 0;
    padding-left: 20px;
}

.messages .ai li {
    margin: 4px 0;
}

/* Improve paragraph spacing */
.messages .ai p {
    margin: 8px 0;
}

.messages .ai p:first-child {
    margin-top: 0;
}

.messages .ai p:last-child {
    margin-bottom: 0;
}

.messages .ai code {
    background: rgba(255, 255, 255, 0.1);
    padding: 3px 6px;
    border-radius: 6px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 0.9em;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.messages .ai pre {
    background: rgba(0, 0, 0, 0.3);
    padding: 16px;
    border-radius: 12px;
    overflow-x: auto;
    margin: 12px 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
}

.messages .ai pre code {
    background: transparent;
    padding: 0;
    border-radius: 0;
    border: none;
    font-size: 0.85em;
    line-height: 1.5;
}

/* Scrollbar for code blocks */
.messages .ai pre::-webkit-scrollbar {
    height: 8px;
}

.messages .ai pre::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.messages .ai pre::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

.messages .ai pre::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.4);
}

/* Copy button styles */
.copy-button {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10px);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 6px 10px;
    cursor: pointer;
    font-size: 0.75em;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 10;
    font-weight: 500;
}

.copy-button:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.05) translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Show copy button on message hover */
.messages .ai:hover .copy-button-message {
    opacity: 1;
    animation: buttonGlow 0.3s ease-out;
}

/* Show copy button on code block hover */
.messages .ai pre:hover .copy-button-code {
    opacity: 1;
    animation: buttonGlow 0.3s ease-out;
}

/* Specific styling for code block copy buttons */
.copy-button-code {
    top: 10px;
    right: 10px;
    background: rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.3);
}

.copy-button-code:hover {
    background: rgba(102, 126, 234, 0.4);
    border-color: rgba(102, 126, 234, 0.5);
}

@keyframes buttonGlow {
    0% {
        box-shadow: 0 0 0 rgba(102, 126, 234, 0.4);
    }
    50% {
        box-shadow: 0 0 15px rgba(102, 126, 234, 0.4);
    }
    100% {
        box-shadow: 0 0 0 rgba(102, 126, 234, 0.4);
    }
}

/* Edit button styles */
.edit-button {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(102, 126, 234, 0.7);
    backdrop-filter: blur(10px);
    color: white;
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 8px;
    padding: 6px 10px;
    cursor: pointer;
    font-size: 0.75em;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 10;
    font-weight: 500;
}

.edit-button:hover {
    background: rgba(102, 126, 234, 0.9);
    transform: scale(1.05) translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.messages .user:hover .edit-button {
    opacity: 1;
    animation: buttonGlow 0.3s ease-out;
}

/* Edit textarea styles */
.edit-textarea {
    width: 100%;
    min-height: 60px;
    background-color: #2c2c2e;
    color: white;
    border: 1px solid #0a84ff;
    border-radius: 8px;
    padding: 8px;
    font-family: inherit;
    font-size: inherit;
    resize: vertical;
    margin-bottom: 8px;
}

.edit-textarea:focus {
    outline: none;
    border-color: #0066cc;
    box-shadow: 0 0 0 2px rgba(10, 132, 255, 0.3);
}

/* Edit action buttons */
.save-edit-button, .cancel-edit-button {
    background-color: #0a84ff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    margin-right: 8px;
    cursor: pointer;
    font-size: 0.85em;
    transition: background-color 0.2s ease;
}

.save-edit-button:hover {
    background-color: #006ce0;
}

.cancel-edit-button {
    background-color: #6c757d;
}

.cancel-edit-button:hover {
    background-color: #5a6268;
}

/* Error message styles */
.messages .ai.error {
    background-color: #dc3545 !important;
    color: white;
    border-left: 4px solid #a71e2a;
}


.input-area {
    display: flex;
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(20, 20, 20, 0.8);
    backdrop-filter: blur(20px);
    gap: 12px;
    position: relative;
    z-index: 1;
}

.input-area textarea {
    flex: 1;
    padding: 16px 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    background: rgba(30, 30, 30, 0.8);
    color: white;
    font-size: 16px;
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    resize: none;
    font-family: inherit;
    line-height: 1.5;
    min-height: 50px;
    height: 50px;
    max-height: 200px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.input-area textarea::-webkit-scrollbar {
    width: 6px;
}

.input-area textarea::-webkit-scrollbar-track {
    background: transparent;
}

.input-area textarea::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.input-area textarea:focus {
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 0 20px rgba(102, 126, 234, 0.2);
    transform: translateY(-1px);
}

.input-area textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.input-area button {
    padding: 16px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    min-width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.input-area button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.input-area button:active {
    transform: translateY(0);
}

/* Global glow effects */
@keyframes globalGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.2);
    }
    50% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
    }
}

/* Hover effects for messages */
.messages .user:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.messages .ai:hover {
    transform: scale(1.01);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Loading animation for new messages */
@keyframes typing {
    0%, 20% { opacity: 0; }
    50% { opacity: 1; }
    100% { opacity: 0; }
}

.typing-indicator {
    display: flex;
    gap: 4px;
    padding: 16px 20px;
    align-self: flex-start;
    background: rgba(30, 30, 30, 0.8);
    border-radius: 20px 20px 20px 6px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

/* Smooth transitions for all interactive elements */
* {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* Focus styles for accessibility */
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
    outline: 2px solid rgba(102, 126, 234, 0.5);
    outline-offset: 2px;
}

/* Notification system */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 12px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-info {
    background: rgba(102, 126, 234, 0.9);
}

.notification-error {
    background: rgba(220, 53, 69, 0.9);
}

.notification-success {
    background: rgba(40, 167, 69, 0.9);
}
